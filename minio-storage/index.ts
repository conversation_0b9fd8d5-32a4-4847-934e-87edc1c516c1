#!/usr/bin/env bun
import * as Minio from "minio";
import ora from "ora";
import fs from "fs";
import path from "path";
import os from "os";
import { uploadToB2 } from "./b2-storage";


const BUCKET_NAME = process.env.MINIO_BUCKET_NAME ?? "test-bucket";

const minioClient = new Minio.Client({
	endPoint: process.env.MINIO_ENDPOINT ?? "localhost",
	port: process.env.MINIO_PORT ? Number.parseInt(process.env.MINIO_PORT) : 9000,
	useSSL: process.env.MINIO_USE_SSL === "true",
	accessKey: process.env.MINIO_ACCESS_KEY ?? "minioadmin",
	secretKey: process.env.MINIO_SECRET_KEY ?? "minioadmin",
});

async function uploadFile(filePath: string) {
	const fileName = path.basename(filePath);
	const metaData = {
		"Content-Type": "application/octet-stream",
	};

	try {
		const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
		if (!bucketExists) {
			await minioClient.makeBucket(BUCKET_NAME);
			console.log(`Bucket ${BUCKET_NAME} created successfully.`);
		}
	} catch (err) {
		console.error("Error checking or creating bucket:", err);
		process.exit(1);
	}

	const spinner = ora(`Uploading ${fileName}...`).start();
	try {
		await minioClient.fPutObject(
			BUCKET_NAME,
			fileName,
			filePath,
			metaData,
		);
		spinner.succeed(
			`File ${fileName} uploaded successfully to bucket ${BUCKET_NAME}.`,
		);
	} catch (err) {
		spinner.fail("Error uploading file:");
		console.error(err);
		process.exit(1);
	}
}

async function main() {
	const storageProvider = process.argv[3] ?? "minio";
	let filePath = process.argv[2];

	if (filePath && filePath.startsWith("~")) {
		filePath = filePath.replace("~", os.homedir());
	}

	if (!filePath) {
		console.error("Please provide a file path as an argument.");
		process.exit(1);
	}

	if (!fs.existsSync(filePath)) {
		console.error(`File not found: ${filePath}`);
		process.exit(1);
	}

		await uploadToB2(filePath);
}

main();
