
import B2 from "backblaze-b2";
import fs from "node:fs/promises";
import * as process from "node:process";
import ora from "ora";
import path from "node:path";

const BACK_BLAZE_BUCKET =
  process.env.BACK_BLAZE_BUCKET ?? "BACK_BLAZE_BUCKET";
const BACK_BLAZE_ID = process.env.BACK_BLAZE_ID ?? "BACK_BLAZE_ID";
const BACK_BLAZE_KEY = process.env.BACK_BLAZE_KEY ?? "BACK_BLAZE_KEY";

const b2 = new B2({
  applicationKeyId: BACK_BLAZE_ID,
  applicationKey: BACK_BLAZE_KEY,
  axios: {
    timeout: 30000 // 30 seconds timeout for individual requests
  }
});


function humanFileSize(size: number) {
  if (size === 0 || !size || Number.isNaN(size)) return "0 B";
  const i = Math.floor(Math.log(size) / Math.log(1024));
  const units = ["B", "KB", "MB", "GB", "TB"];
  const unitIndex = Math.min(i, units.length - 1);
  return `${(size / (1024 ** unitIndex)).toFixed(2)} ${units[unitIndex]}`;
}

async function uploadToB2(filePath: string, destinationName?: string) {
  const fileName = destinationName || path.basename(filePath);
  const spinner = ora(`Uploading ${fileName} to B2...`).start();

  try {
    const { data: authData } = await b2.authorize(); // Ensure authorization is complete

    const fileStats = await fs.stat(filePath);
    const fileSize = fileStats.size;
    const largeFileThreshold = authData.absoluteMinPartSize || 5 * 1024 * 1024; // Use absolute minimum part size from B2, fallback to 5MB

    console.log(`File size: ${humanFileSize(fileSize)}, Large file threshold: ${humanFileSize(largeFileThreshold)}`);

    // Use simple upload for small files
    if (fileSize < largeFileThreshold) {
      spinner.text = `Uploading ${fileName} to B2... (small file - ${humanFileSize(fileSize)})`;
      console.log(`Starting small file upload for ${fileName} (${humanFileSize(fileSize)})`);

      console.log(`Getting upload URL for small file...`);
      const { data: uploadUrlData } = await Promise.race([
        b2.getUploadUrl({
          bucketId: BACK_BLAZE_BUCKET,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout getting upload URL')), 30000)
        )
      ]) as any;

      console.log(`Reading file content...`);
      const fileContent = await fs.readFile(filePath);

      console.log(`Starting file upload to B2...`);
      await Promise.race([
        b2.uploadFile({
          uploadUrl: uploadUrlData.uploadUrl,
          uploadAuthToken: uploadUrlData.authorizationToken,
          fileName: fileName,
          data: fileContent,
          onUploadProgress: (event: { loaded: number; total: number }) => {
            if (event.total) {
              const percentage = Math.round((event.loaded / event.total) * 100);
              spinner.text = `Uploading ${fileName} to B2... ${percentage}% (${humanFileSize(event.loaded)}/${humanFileSize(event.total)})`;
              console.log(`Upload progress: ${percentage}%`);
            }
          },
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout during file upload')), 60000)
        )
      ]);

      console.log(`Small file upload completed successfully!`);
      spinner.succeed(
        `File ${fileName} uploaded successfully to bucket ${BACK_BLAZE_BUCKET}. Size: ${humanFileSize(fileSize)}`
      );
      return;
    }

    // Use large file upload for large files
    spinner.text = `Uploading ${fileName} to B2... (large file - ${humanFileSize(fileSize)})`;
    console.log(`Starting large file upload for ${fileName} (${humanFileSize(fileSize)})`);

    const {
      data: { fileId },
    } = await b2.startLargeFile({
      bucketId: BACK_BLAZE_BUCKET,
      fileName: fileName,
    });

    console.log(`Large file upload started with fileId: ${fileId}`);

    const chunkSize = largeFileThreshold;
    const sha1s: string[] = [];
    let uploadedBytes = 0;
    let partNumber = 1;

    // Read file in chunks manually to avoid async iterator issues
    const fileHandle = await fs.open(filePath, 'r');

    try {
      while (uploadedBytes < fileSize) {
        const remainingBytes = fileSize - uploadedBytes;
        const currentChunkSize = Math.min(chunkSize, remainingBytes);

        console.log(`Reading chunk ${partNumber}: ${humanFileSize(currentChunkSize)} (offset: ${uploadedBytes})`);

        const buffer = Buffer.alloc(currentChunkSize);
        const { bytesRead } = await fileHandle.read(buffer, 0, currentChunkSize, uploadedBytes);

        if (bytesRead === 0) {
          console.log('No more bytes to read, breaking loop');
          break;
        }

        const chunk = buffer.subarray(0, bytesRead);
        console.log(`Uploading chunk ${partNumber}: ${humanFileSize(chunk.length)}`);

        let retries = 0;
        let success = false;
        let uploadUrl: string | undefined;
        let authorizationToken: string | undefined;

        while (!success && retries < 3) {
          try {
            console.log(`Getting upload URL for part ${partNumber} (attempt ${retries + 1})`);

            const { data } = await b2.getUploadPartUrl({ fileId });
            uploadUrl = data.uploadUrl;
            authorizationToken = data.authorizationToken;

            console.log(`Uploading part ${partNumber} to B2...`);
            const response = await b2.uploadPart({
              partNumber: partNumber,
              uploadUrl: uploadUrl,
              uploadAuthToken: authorizationToken,
              data: chunk,
            });

            console.log(`Part ${partNumber} uploaded successfully. SHA1: ${response.data.contentSha1}`);
            if (response.data.contentSha1) {
              sha1s.push(response.data.contentSha1);
            }
            success = true;
          } catch (err) {
            console.error(`Error uploading part ${partNumber} (attempt ${retries + 1}):`, err);
            retries++;
            uploadUrl = undefined; // Reset to get a new URL
            if (retries >= 3) {
              throw new Error(`Failed to upload part ${partNumber} after 3 attempts: ${err}`);
            }
            console.log(`Retrying part ${partNumber} in 2 seconds...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }

        uploadedBytes += bytesRead;
        const percentage = Math.round((uploadedBytes / fileSize) * 100);
        spinner.text = `Uploading ${fileName} to B2... ${percentage}% (${humanFileSize(uploadedBytes)}/${humanFileSize(fileSize)})`;
        console.log(`Progress: ${percentage}% (${humanFileSize(uploadedBytes)}/${humanFileSize(fileSize)})`);
        partNumber++;
      }
    } finally {
      await fileHandle.close();
    }

    console.log(`Finishing large file upload with ${sha1s.length} parts...`);
    spinner.text = `Finalizing upload of ${fileName}...`;

    await b2.finishLargeFile({
      fileId: fileId,
      partSha1Array: sha1s,
    });

    console.log(`Large file upload completed successfully!`);
    spinner.succeed(
      `File ${fileName} uploaded successfully to bucket ${BACK_BLAZE_BUCKET}. Total size: ${humanFileSize(fileSize)}`
    );
  } catch (err) {
    spinner.fail("Error uploading file to B2:");
    console.error(err);
    process.exit(1);
  }
}

export { uploadToB2 };
