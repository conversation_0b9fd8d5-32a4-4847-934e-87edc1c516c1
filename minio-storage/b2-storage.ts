
import B2 from "backblaze-b2";
import { createReadStream } from "node:fs";
import fs from "node:fs/promises";
import * as process from "node:process";
import ora from "ora";
import path from "node:path";

const BACK_BLAZE_BUCKET =
  process.env.BACK_BLAZE_BUCKET ?? "BACK_BLAZE_BUCKET";
const BACK_BLAZE_ID = process.env.BACK_BLAZE_ID ?? "BACK_BLAZE_ID";
const BACK_BLAZE_KEY = process.env.BACK_BLAZE_KEY ?? "BACK_BLAZE_KEY";

const b2 = new B2({
  applicationKeyId: BACK_BLAZE_ID,
  applicationKey: BACK_BLAZE_KEY,
  axios: {
    timeout: 7200000
  }
});


function humanFileSize(size: number) {
  if (size === 0) return "0 B";
  const i = Math.floor(Math.log(size) / Math.log(1024));
  return `${(size / Math.pow(1024, i)).toFixed(2)} ${["B", "KB", "MB", "GB", "TB"][i]}`;
}

async function uploadToB2(filePath: string) {
  const fileName = path.basename(filePath);
  const spinner = ora(`Uploading ${fileName} to B2...`).start();

  try {
    const { data: authData } = await b2.authorize(); // Ensure authorization is complete

    const fileStats = await fs.stat(filePath);
    const fileSize = fileStats.size;
    const largeFileThreshold = authData.absoluteMinPartSize; // Use absolute minimum part size from B2

    // Use simple upload for small files
    if (fileSize < largeFileThreshold) {
      spinner.text = `Uploading ${fileName} to B2... (small file)`;
      const { data: uploadUrlData } = await b2.getUploadUrl({
        bucketId: BACK_BLAZE_BUCKET,
      });

      const fileContent = await fs.readFile(filePath);

      await b2.uploadFile({
        uploadUrl: uploadUrlData.uploadUrl,
        uploadAuthToken: uploadUrlData.authorizationToken,
        fileName: fileName,
        data: fileContent,
        onUploadProgress: (event: { loaded: number; total: number }) => {
          if (event.total) {
            const percentage = Math.round((event.loaded / event.total) * 100);
            spinner.text = `Uploading ${fileName} to B2... ${percentage}%`;
          }
        },
      });

      spinner.succeed(
        `File ${fileName} uploaded successfully to bucket ${BACK_BLAZE_BUCKET}.`
      );
      return;
    }

    // Use large file upload for large files
    spinner.text = `Uploading ${fileName} to B2... (large file)`;
    const {
      data: { fileId },
    } = await b2.startLargeFile({
      bucketId: BACK_BLAZE_BUCKET,
      fileName: fileName,
    });

    const chunkSize = largeFileThreshold;
    const sha1s: string[] = [];
    let uploadedBytes = 0;

    const fileStream = createReadStream(filePath, { highWaterMark: chunkSize });

    let partNumber = 1;

    for await (const chunk of fileStream) {
      console.log(`Uploading chunk: ${humanFileSize(chunk.length)}`);
      let retries = 0;
      let success = false;
      let uploadUrl, authorizationToken;

      while (!success && retries < 3) {
        try {
          if (!uploadUrl) {
            const { data } = await b2.getUploadPartUrl({ fileId });
            uploadUrl = data.uploadUrl;
            authorizationToken = data.authorizationToken;
          }

          const response = await b2.uploadPart({
            partNumber: partNumber,
            uploadUrl: uploadUrl,
            uploadAuthToken: authorizationToken,
            data: chunk,
          });

          spinner.text = `Uploading ${response.data.contentSha1} to B2...`;
          if (response.data.contentSha1) {
            sha1s.push(response.data.contentSha1);
          }
          success = true;
        } catch (err) {
          console.error(err , 'err')
          retries++;
          uploadUrl = null; // Reset to get a new URL
          if (retries >= 3) {
            throw err; // Rethrow after max retries
          }
        }
      }

      uploadedBytes += chunk.length;
      const percentage = Math.round((uploadedBytes / fileSize) * 100);
      spinner.text = `Uploading ${fileName} to B2... ${percentage}%`;
      partNumber++;
    }

    await b2.finishLargeFile({
      fileId: fileId,
      partSha1Array: sha1s,
    });

    spinner.succeed(
      `File ${fileName} uploaded successfully to bucket ${BACK_BLAZE_BUCKET}.`
    );
  } catch (err) {
    spinner.fail("Error uploading file to B2:");
    console.error(err);
    process.exit(1);
  }
}

export { uploadToB2 };
