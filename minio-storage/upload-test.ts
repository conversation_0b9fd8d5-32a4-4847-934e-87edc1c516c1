import { uploadToB2 } from "./b2-storage";
import fs from "fs";
import crypto from "crypto";

const TEST_FILE_NAME = "large-test-file.bin";
const TEST_FILE_SIZE_MB = 10; // Using a smaller size for a test case to run faster
const TEST_FILE_SIZE_BYTES = TEST_FILE_SIZE_MB * 1024 * 1024;

async function createTestFile() {
  console.log(`Creating a ${TEST_FILE_SIZE_MB}MB test file...`);
  const buffer = crypto.randomBytes(TEST_FILE_SIZE_BYTES);
  fs.writeFileSync(TEST_FILE_NAME, buffer);
  console.log("Test file created.");
}

async function runTest() {
  await createTestFile();
  console.log("Starting upload test...");
  try {
    await uploadToB2(TEST_FILE_NAME);
    console.log("Upload test completed successfully.");
  } catch (error) {
    console.error("Upload test failed:", error);
  } finally {
    console.log("Cleaning up test file...");
    fs.unlinkSync(TEST_FILE_NAME);
    console.log("Test file deleted.");
  }
}

runTest();
